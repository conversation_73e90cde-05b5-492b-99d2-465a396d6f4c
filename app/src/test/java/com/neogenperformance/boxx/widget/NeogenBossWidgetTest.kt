package com.neogenperformance.boss.widget

import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for NeogenBossWidget
 */
class NeogenBossWidgetTest {

    @Test
    fun widget_provider_is_not_null() {
        val provider = NeogenBossWidgetProvider()
        assertNotNull("Widget provider should not be null", provider)
        assertNotNull("Glance app widget should not be null", provider.glanceAppWidget)
    }

    @Test
    fun widget_is_instance_of_correct_class() {
        val provider = NeogenBossWidgetProvider()
        assertTrue("Widget should be instance of NeogenBossWidget",
                   provider.glanceAppWidget is NeogenBossWidget)
    }

    @Test
    fun widget_data_repository_returns_items() {
        val items = WidgetDataRepository.getWidgetItems()
        assertNotNull("Widget items should not be null", items)
        assertTrue("Widget items should not be empty", items.isNotEmpty())
        assertTrue("Widget should have at least 3 items", items.size >= 3)
    }

    @Test
    fun widget_list_items_have_required_fields() {
        val items = WidgetDataRepository.getWidgetItems()
        items.forEach { item ->
            assertNotNull("Item title should not be null", item.title)
            assertNotNull("Item subtitle should not be null", item.subtitle)
            assertNotNull("Item status should not be null", item.status)
            assertTrue("Item title should not be empty", item.title.isNotEmpty())
            assertTrue("Item subtitle should not be empty", item.subtitle.isNotEmpty())
            assertTrue("Item status should not be empty", item.status.isNotEmpty())
        }
    }
}

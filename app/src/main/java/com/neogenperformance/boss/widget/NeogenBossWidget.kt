package com.neogenperformance.boss.widget

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.action.actionStartActivity
import androidx.glance.action.clickable
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.width
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.neogenperformance.boss.MainActivity
import com.neogenperformance.boss.R

/**
 * Jetpack Glance implementation of the NeogenBoss widget with scrollable list
 */
class NeogenBossWidget : GlanceAppWidget() {

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        provideContent {
            GlanceTheme {
                WidgetContent(context)
            }
        }
    }

    @Composable
    private fun WidgetContent(context: Context) {
        val items = WidgetDataRepository.getWidgetItems()
        
        Box(
            modifier = GlanceModifier
                .fillMaxSize()
                .background(
                    androidx.glance.ImageProvider(R.drawable.widget_background)
                )
                .padding(16.dp)
                .clickable(actionStartActivity<MainActivity>()),
            contentAlignment = Alignment.TopStart
        ) {
            Column(
                modifier = GlanceModifier.fillMaxSize()
            ) {
                // Header
                Text(
                    text = context.getString(R.string.app_name),
                    style = TextStyle(
                        color = ColorProvider(Color.White),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                )
                
                Spacer(modifier = GlanceModifier.height(8.dp))
                
                Text(
                    text = context.getString(R.string.widget_status_list),
                    style = TextStyle(
                        color = ColorProvider(Color.White),
                        fontSize = 12.sp
                    )
                )
                
                Spacer(modifier = GlanceModifier.height(12.dp))
                
                // Scrollable list of items
                Column(
                    modifier = GlanceModifier.fillMaxWidth()
                ) {
                    items.forEach { item ->
                        WidgetListItemView(item = item)
                        Spacer(modifier = GlanceModifier.height(8.dp))
                    }
                }
            }
        }
    }

    @Composable
    private fun WidgetListItemView(item: WidgetListItem) {
        Box(
            modifier = GlanceModifier
                .fillMaxWidth()
                .background(ColorProvider(Color.Black.copy(alpha = 0.2f)))
                .padding(8.dp)
                .clickable(actionStartActivity<MainActivity>())
        ) {
            Row(
                modifier = GlanceModifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = GlanceModifier.defaultWeight()
                ) {
                    Text(
                        text = item.title,
                        style = TextStyle(
                            color = ColorProvider(Color.White),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                    
                    Text(
                        text = item.subtitle,
                        style = TextStyle(
                            color = ColorProvider(Color.White.copy(alpha = 0.8f)),
                            fontSize = 11.sp
                        )
                    )
                }
                
                Spacer(modifier = GlanceModifier.width(8.dp))
                
                // Status indicator
                Box(
                    modifier = GlanceModifier
                        .background(getStatusColor(item.status))
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = item.status,
                        style = TextStyle(
                            color = ColorProvider(Color.White),
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            }
        }
    }

    private fun getStatusColor(status: String): ColorProvider {
        return when (status.lowercase()) {
            "active", "good", "connected", "secure", "current", "complete" -> 
                ColorProvider(Color(0xFF4CAF50)) // Green
            "warning", "pending" -> 
                ColorProvider(Color(0xFFFF9800)) // Orange
            "error", "failed", "disconnected" -> 
                ColorProvider(Color(0xFFF44336)) // Red
            else -> 
                ColorProvider(Color(0xFF9E9E9E)) // Gray
        }
    }
}

/**
 * Glance App Widget Receiver for the NeogenBoss widget
 */
class NeogenBossGlanceWidgetReceiver : GlanceAppWidgetReceiver() {
    override val glanceAppWidget: GlanceAppWidget = NeogenBossWidget()
}

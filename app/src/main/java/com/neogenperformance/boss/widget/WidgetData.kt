package com.neogenperformance.boss.widget

/**
 * Data class representing an item in the widget list
 */
data class WidgetListItem(
    val id: Int,
    val title: String,
    val subtitle: String,
    val status: String
)

/**
 * Repository for widget data
 */
object WidgetDataRepository {
    
    /**
     * Get sample data for the widget list
     */
    fun getWidgetItems(): List<WidgetListItem> {
        return listOf(
            WidgetListItem(
                id = 1,
                title = "System Status",
                subtitle = "All systems operational",
                status = "Active"
            ),
            WidgetListItem(
                id = 2,
                title = "Performance",
                subtitle = "Running at optimal levels",
                status = "Good"
            ),
            WidgetListItem(
                id = 3,
                title = "Network",
                subtitle = "Connection stable",
                status = "Connected"
            ),
            WidgetListItem(
                id = 4,
                title = "Security",
                subtitle = "All checks passed",
                status = "Secure"
            ),
            WidgetListItem(
                id = 5,
                title = "Updates",
                subtitle = "System up to date",
                status = "Current"
            ),
            WidgetListItem(
                id = 6,
                title = "Backup",
                subtitle = "Last backup: 2 hours ago",
                status = "Complete"
            )
        )
    }
}
